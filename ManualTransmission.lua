-- Manual Transmission Script for Roblox
-- Realistic manual transmission system with clutch, gear shifting, and engine stalling

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")

-- Manual Transmission Class
local ManualTransmission = {}
ManualTransmission.__index = ManualTransmission

function ManualTransmission.new(vehicle)
    local self = setmetatable({}, ManualTransmission)
    
    -- Vehicle reference
    self.vehicle = vehicle
    self.bodyVelocity = vehicle.PrimaryPart:FindFirstChild("BodyVelocity") or Instance.new("BodyVelocity")
    self.bodyAngularVelocity = vehicle.PrimaryPart:FindFirstChild("BodyAngularVelocity") or Instance.new("BodyAngularVelocity")
    
    -- Engine properties
    self.engineRPM = 800 -- Idle RPM
    self.maxRPM = 7000
    self.idleRPM = 800
    self.redlineRPM = 6500
    self.engineRunning = true
    self.engineStalled = false
    
    -- Transmission properties
    self.currentGear = 0 -- 0 = Neutral, -1 = Reverse, 1-6 = Forward gears
    self.maxGears = 6
    self.clutchPressed = false
    self.clutchPosition = 0 -- 0 = fully engaged, 1 = fully disengaged
    
    -- Gear ratios (higher number = more torque, less speed)
    self.gearRatios = {
        [-1] = -3.5, -- Reverse
        [0] = 0,     -- Neutral
        [1] = 3.8,   -- 1st gear
        [2] = 2.4,   -- 2nd gear
        [3] = 1.7,   -- 3rd gear
        [4] = 1.3,   -- 4th gear
        [5] = 1.0,   -- 5th gear
        [6] = 0.8    -- 6th gear (overdrive)
    }
    
    -- Physics properties
    self.throttleInput = 0
    self.brakeInput = 0
    self.speed = 0
    self.maxSpeed = 200 -- studs per second
    self.engineTorque = 300
    self.brakePower = 50
    
    -- Input tracking
    self.keysPressed = {}
    
    -- Setup body movers
    self.bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000)
    self.bodyVelocity.Velocity = Vector3.new(0, 0, 0)
    self.bodyVelocity.Parent = vehicle.PrimaryPart
    
    self.bodyAngularVelocity.MaxTorque = Vector3.new(0, 4000, 0)
    self.bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
    self.bodyAngularVelocity.Parent = vehicle.PrimaryPart
    
    -- Create GUI
    self:createGUI()
    
    -- Connect events
    self:connectInputs()
    self:startUpdateLoop()
    
    return self
end

function ManualTransmission:createGUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "TransmissionGUI"
    screenGui.Parent = player.PlayerGui
    
    -- Main frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 300, 0, 200)
    mainFrame.Position = UDim2.new(0, 10, 1, -210)
    mainFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    mainFrame.BackgroundTransparency = 0.3
    mainFrame.BorderSizePixel = 2
    mainFrame.BorderColor3 = Color3.new(1, 1, 1)
    mainFrame.Parent = screenGui
    
    -- RPM gauge
    local rpmLabel = Instance.new("TextLabel")
    rpmLabel.Size = UDim2.new(1, 0, 0, 30)
    rpmLabel.Position = UDim2.new(0, 0, 0, 0)
    rpmLabel.BackgroundTransparency = 1
    rpmLabel.Text = "RPM: 800"
    rpmLabel.TextColor3 = Color3.new(1, 1, 1)
    rpmLabel.TextScaled = true
    rpmLabel.Font = Enum.Font.SourceSansBold
    rpmLabel.Parent = mainFrame
    
    -- Gear indicator
    local gearLabel = Instance.new("TextLabel")
    gearLabel.Size = UDim2.new(1, 0, 0, 40)
    gearLabel.Position = UDim2.new(0, 0, 0, 35)
    gearLabel.BackgroundTransparency = 1
    gearLabel.Text = "GEAR: N"
    gearLabel.TextColor3 = Color3.new(0, 1, 0)
    gearLabel.TextScaled = true
    gearLabel.Font = Enum.Font.SourceSansBold
    gearLabel.Parent = mainFrame
    
    -- Speed indicator
    local speedLabel = Instance.new("TextLabel")
    speedLabel.Size = UDim2.new(1, 0, 0, 30)
    speedLabel.Position = UDim2.new(0, 0, 0, 80)
    speedLabel.BackgroundTransparency = 1
    speedLabel.Text = "SPEED: 0 mph"
    speedLabel.TextColor3 = Color3.new(1, 1, 1)
    speedLabel.TextScaled = true
    speedLabel.Font = Enum.Font.SourceSans
    speedLabel.Parent = mainFrame
    
    -- Clutch indicator
    local clutchLabel = Instance.new("TextLabel")
    clutchLabel.Size = UDim2.new(1, 0, 0, 25)
    clutchLabel.Position = UDim2.new(0, 0, 0, 115)
    clutchLabel.BackgroundTransparency = 1
    clutchLabel.Text = "CLUTCH: ENGAGED"
    clutchLabel.TextColor3 = Color3.new(1, 1, 1)
    clutchLabel.TextScaled = true
    clutchLabel.Font = Enum.Font.SourceSans
    clutchLabel.Parent = mainFrame
    
    -- Controls info
    local controlsLabel = Instance.new("TextLabel")
    controlsLabel.Size = UDim2.new(1, 0, 0, 55)
    controlsLabel.Position = UDim2.new(0, 0, 0, 145)
    controlsLabel.BackgroundTransparency = 1
    controlsLabel.Text = "W/S: Throttle/Brake | A/D: Steer\nQ/E: Shift Down/Up | Space: Clutch\nR: Start Engine"
    controlsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    controlsLabel.TextScaled = true
    controlsLabel.Font = Enum.Font.SourceSans
    controlsLabel.Parent = mainFrame
    
    -- Store GUI elements
    self.gui = {
        screenGui = screenGui,
        rpmLabel = rpmLabel,
        gearLabel = gearLabel,
        speedLabel = speedLabel,
        clutchLabel = clutchLabel
    }
end

function ManualTransmission:connectInputs()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        local keyCode = input.KeyCode
        self.keysPressed[keyCode] = true
        
        -- Gear shifting
        if keyCode == Enum.KeyCode.Q then
            self:shiftDown()
        elseif keyCode == Enum.KeyCode.E then
            self:shiftUp()
        elseif keyCode == Enum.KeyCode.R then
            self:startEngine()
        end
    end)
    
    UserInputService.InputEnded:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        local keyCode = input.KeyCode
        self.keysPressed[keyCode] = false
    end)
end

function ManualTransmission:startUpdateLoop()
    RunService.Heartbeat:Connect(function(deltaTime)
        self:update(deltaTime)
    end)
end
