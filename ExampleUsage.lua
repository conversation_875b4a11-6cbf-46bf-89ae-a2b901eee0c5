-- Example usage of the Manual Transmission System
-- This script shows how to set up and use the manual transmission with a vehicle

local ManualTransmission = require(script.Parent.ManualTransmission)

-- Example: Create a simple car model and attach the transmission
local function createExampleCar()
    local car = Instance.new("Model")
    car.Name = "ManualCar"
    car.Parent = workspace
    
    -- Create main body
    local body = Instance.new("Part")
    body.Name = "Body"
    body.Size = Vector3.new(6, 2, 12)
    body.Material = Enum.Material.SmoothPlastic
    body.BrickColor = BrickColor.new("Bright red")
    body.TopSurface = Enum.SurfaceType.Smooth
    body.BottomSurface = Enum.SurfaceType.Smooth
    body.CanCollide = true
    body.Position = Vector3.new(0, 10, 0)
    body.Parent = car
    
    -- Set as PrimaryPart
    car.PrimaryPart = body
    
    -- Create wheels (visual only for this example)
    local wheelPositions = {
        Vector3.new(-2.5, -1.5, -4),  -- Front left
        Vector3.new(2.5, -1.5, -4),   -- Front right
        Vector3.new(-2.5, -1.5, 4),   -- Rear left
        Vector3.new(2.5, -1.5, 4)     -- Rear right
    }
    
    for i, pos in ipairs(wheelPositions) do
        local wheel = Instance.new("Part")
        wheel.Name = "Wheel" .. i
        wheel.Size = Vector3.new(2, 2, 2)
        wheel.Shape = Enum.PartType.Cylinder
        wheel.Material = Enum.Material.Rubber
        wheel.BrickColor = BrickColor.new("Really black")
        wheel.TopSurface = Enum.SurfaceType.Smooth
        wheel.BottomSurface = Enum.SurfaceType.Smooth
        wheel.CanCollide = true
        wheel.Parent = car
        
        -- Position relative to body
        local weld = Instance.new("WeldConstraint")
        weld.Part0 = body
        weld.Part1 = wheel
        weld.Parent = body
        
        wheel.CFrame = body.CFrame * CFrame.new(pos)
    end
    
    -- Add a seat for the player
    local seat = Instance.new("Seat")
    seat.Name = "DriverSeat"
    seat.Size = Vector3.new(2, 1, 2)
    seat.Material = Enum.Material.Fabric
    seat.BrickColor = BrickColor.new("Really black")
    seat.TopSurface = Enum.SurfaceType.Smooth
    seat.BottomSurface = Enum.SurfaceType.Smooth
    seat.CanCollide = true
    seat.Parent = car
    
    -- Position seat
    local seatWeld = Instance.new("WeldConstraint")
    seatWeld.Part0 = body
    seatWeld.Part1 = seat
    seatWeld.Parent = body
    
    seat.CFrame = body.CFrame * CFrame.new(0, 1.5, 2)
    
    return car
end

-- Example: Set up the transmission when player sits in the car
local function setupTransmissionForPlayer(player, vehicle)
    local transmission = ManualTransmission.createForVehicle(vehicle)
    
    if transmission then
        print("Manual transmission system activated for " .. player.Name)
        print("Controls:")
        print("W/S - Throttle/Brake")
        print("A/D - Steer left/right")
        print("Space - Clutch")
        print("Q/E - Shift down/up")
        print("R - Start engine")
        print("")
        print("Tips:")
        print("- Always use the clutch when shifting gears")
        print("- Don't let the RPM get too low in gear or the engine will stall")
        print("- Start in 1st gear for best acceleration")
        print("- Use higher gears for higher speeds")
        
        -- Store transmission reference for cleanup
        player:SetAttribute("ManualTransmission", transmission)
        
        return transmission
    else
        warn("Failed to create manual transmission for vehicle")
        return nil
    end
end

-- Example: Clean up transmission when player leaves
local function cleanupTransmission(player)
    local transmission = player:GetAttribute("ManualTransmission")
    if transmission and transmission.destroy then
        transmission:destroy()
        player:SetAttribute("ManualTransmission", nil)
        print("Manual transmission system deactivated for " .. player.Name)
    end
end

-- Main example function
local function runExample()
    local Players = game:GetService("Players")
    
    -- Create example car
    local car = createExampleCar()
    local seat = car:FindFirstChild("DriverSeat")
    
    if not seat then
        warn("No driver seat found in car!")
        return
    end
    
    -- Handle player sitting/leaving
    seat:GetPropertyChangedSignal("Occupant"):Connect(function()
        local occupant = seat.Occupant
        
        if occupant then
            -- Player sat down
            local player = Players:GetPlayerFromCharacter(occupant.Parent)
            if player then
                wait(0.1) -- Small delay to ensure everything is set up
                setupTransmissionForPlayer(player, car)
            end
        else
            -- Player left seat - find which player had the transmission
            for _, player in pairs(Players:GetPlayers()) do
                if player:GetAttribute("ManualTransmission") then
                    cleanupTransmission(player)
                    break
                end
            end
        end
    end)
    
    print("Example car created! Sit in the driver's seat to activate manual transmission.")
end

-- Auto-run example if this script is executed directly
if script.Parent == workspace or script.Parent == game.ServerScriptService then
    runExample()
end

-- Export functions for use in other scripts
return {
    createExampleCar = createExampleCar,
    setupTransmissionForPlayer = setupTransmissionForPlayer,
    cleanupTransmission = cleanupTransmission,
    runExample = runExample
}
