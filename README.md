# Realistic Manual Transmission System for Roblox

A comprehensive manual transmission system that provides realistic driving mechanics including clutch control, gear shifting, engine stalling, and proper RPM management.

## Features

### Realistic Engine Mechanics
- **RPM System**: Dynamic RPM calculation based on throttle input, gear ratios, and vehicle speed
- **Engine Stalling**: Engine will stall if RPM drops too low while in gear without clutch
- **Redline Protection**: Visual and audio feedback when approaching maximum RPM
- **Idle Control**: Proper idle RPM management when in neutral or clutch disengaged

### Manual Transmission
- **6-Speed Transmission**: Forward gears 1-6 plus reverse and neutral
- **Realistic Gear Ratios**: Each gear has appropriate ratios for acceleration vs top speed
- **Clutch System**: Smooth clutch engagement/disengagement with realistic timing
- **Gear Protection**: Cannot shift without pressing clutch pedal

### Physics System
- **Torque Curves**: Realistic engine torque delivery based on RPM
- **Speed Calculation**: Proper speed calculation based on gear ratios and engine RPM
- **Braking System**: Independent braking system with realistic deceleration
- **Air Resistance**: Speed-dependent air resistance for realistic top speeds

### User Interface
- **RPM Gauge**: Real-time RPM display with color-coded redline warning
- **Gear Indicator**: Current gear display (N, R, 1-6)
- **Speed Display**: Current speed in MPH
- **Clutch Status**: Visual indication of clutch engagement
- **Control Instructions**: Built-in help display

## Controls

| Key | Function |
|-----|----------|
| **W** | Throttle (Accelerate) |
| **S** | Brake |
| **A** | Steer Left |
| **D** | Steer Right |
| **Space** | Clutch Pedal |
| **Q** | Shift Down |
| **E** | Shift Up |
| **R** | Start Engine |

## Installation

1. Place `ManualTransmission.lua` in your game as a ModuleScript
2. Use `ExampleUsage.lua` as a reference for implementation
3. Require the module in your vehicle scripts

## Basic Usage

```lua
local ManualTransmission = require(path.to.ManualTransmission)

-- Create transmission for a vehicle
local transmission = ManualTransmission.createForVehicle(yourVehicleModel)
```

## Requirements

Your vehicle model must have:
- A **PrimaryPart** set (usually the main chassis/body)
- The PrimaryPart should be the main physics body of the vehicle

## Driving Tips

### Starting the Engine
1. Make sure you're in **Neutral** (gear N) or press the **Clutch** (Space)
2. Press **R** to start the engine
3. You'll hear confirmation when the engine starts

### Getting Moving
1. Press and hold the **Clutch** (Space)
2. Shift to **1st gear** (press E once from neutral)
3. Slowly release the clutch while gently applying throttle (W)
4. Once moving, you can shift to higher gears for more speed

### Shifting Gears
1. Always press the **Clutch** before shifting
2. **Lower gears** (1-2) for acceleration and hills
3. **Higher gears** (3-6) for cruising and top speed
4. **6th gear** is overdrive - best for highway speeds

### Avoiding Stalls
- Don't let RPM drop below 500 while in gear
- Use the clutch when coming to a stop
- Downshift when slowing down
- If you stall, put the car in neutral and press R to restart

## Gear Ratios

| Gear | Ratio | Best For |
|------|-------|----------|
| **R** | -3.5 | Reverse |
| **N** | 0.0 | Neutral/Parking |
| **1** | 3.8 | Starting, steep hills |
| **2** | 2.4 | Low speed acceleration |
| **3** | 1.7 | City driving |
| **4** | 1.3 | Highway entrance |
| **5** | 1.0 | Highway cruising |
| **6** | 0.8 | High speed efficiency |

## Customization

You can modify these properties in the ManualTransmission class:

```lua
-- Engine properties
self.maxRPM = 7000        -- Maximum engine RPM
self.idleRPM = 800        -- Idle RPM
self.redlineRPM = 6500    -- Redline warning RPM
self.engineTorque = 300   -- Engine torque output

-- Transmission properties
self.maxGears = 6         -- Number of forward gears
self.gearRatios = {...}   -- Gear ratio table

-- Physics properties
self.maxSpeed = 200       -- Maximum speed (studs/second)
self.brakePower = 50      -- Braking force multiplier
```

## Advanced Features

### Engine Stall Recovery
If your engine stalls:
1. The RPM will drop to 0
2. Put the transmission in **Neutral** (or hold clutch)
3. Press **R** to restart the engine
4. Wait for RPM to stabilize at idle before driving

### Performance Driving
- **Rev matching**: Blip the throttle when downshifting for smoother transitions
- **Heel-toe**: Use brake and throttle simultaneously for advanced techniques
- **Launch control**: Start in 1st gear with optimal RPM for best acceleration

## Troubleshooting

**Engine won't start:**
- Make sure you're in neutral or holding the clutch
- Check that the engine isn't already running

**Car won't move:**
- Ensure you're not in neutral
- Check that the clutch is engaged (not pressed)
- Verify the engine is running

**Engine keeps stalling:**
- Don't let RPM drop too low in gear
- Use the clutch when stopping
- Downshift when slowing down

## License

This manual transmission system is provided as-is for educational and entertainment purposes in Roblox games.
